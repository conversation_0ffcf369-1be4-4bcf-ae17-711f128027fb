import React from "react";
import { render, screen, waitFor, fireEvent} from "@testing-library/react";
import MakingCharges from "./MakingCharges";
import { useAuth } from "../../Context/AuthContext";
import MakingChargesAPI from "../../Utils/MakingChargesApi";
import userEvent from "@testing-library/user-event";

// Mock useAuth
jest.mock("../../Context/AuthContext", () => ({
  useAuth: jest.fn(),
}));

// Mock API
jest.mock("../../Utils/MakingChargesApi", () => ({
  fetchMakingCharges: jest.fn(() =>
    Promise.resolve([
      {
        _id: "67bc7318003e339ecc26b5f5",
        minWeight: 0,
        maxWeight: 3,
        makingChargePerGram: 2500,
        updatedAt: "2025-03-05T10:13:45.391+00:00",
      },
    ])
  ),
  updateMakingCharges: jest.fn(() => Promise.resolve({ success: true })),
  addMakingCharge: jest.fn(() => Promise.resolve({ success: true }))
}));


// Mocking axios for the specific API call
jest.mock("axios");

beforeEach(() => {
  useAuth.mockReturnValue({
    toggleSidebar: jest.fn(),
    isDesktop: true,
  });
});

describe("MakingCharges Component", () => {
  // 1st Test Case: renders correctly and loads data
  test("renders correctly and loads data", async () => {
    render(<MakingCharges />);

    // Wait for API call to complete
    await waitFor(() => expect(MakingChargesAPI.fetchMakingCharges).toHaveBeenCalled());

    // Wait for UI to update
    await waitFor(() => {
      expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
    });

    // Wait for the "Making Charges" heading (specifically an <h1> element)
    await waitFor(() => {
      const headings = screen.getAllByText(/Making Charges/i);
      expect(headings.length).toBeGreaterThan(0);
      expect(headings[0].tagName).toBe("H1");
    });

    // Ensure API data is displayed
    await waitFor(() => {
      expect(screen.getByText(/0 - 3/i)).toBeInTheDocument(); 
      expect(screen.getByText(/₹2500/i)).toBeInTheDocument();
    });
  });

  // 2nd Test Case: updates making charges correctly
  test("updates making charges correctly", async () => {
    render(<MakingCharges />);

    // Wait for initial API data load
    await waitFor(() => expect(MakingChargesAPI.fetchMakingCharges).toHaveBeenCalled());

    const editButton = await screen.findByRole("button", { name: /edit making charges/i });
    await userEvent.click(editButton);

    // Mocking new data
    const updatePayload = [
      { id: "67bc7318003e339ecc26b5f5", minWeight: 0, maxWeight: 3, makingChargePerGram: 2600 },
      { id: "67bc7318003e339ecc26b5f6", minWeight: 3, maxWeight: null, makingChargePerGram: 2800 },
    ];

    // Simulate updating input fields
    const inputFields = screen.getAllByRole("spinbutton");
    await userEvent.clear(inputFields[0]);
    await userEvent.type(inputFields[0], "2600");

    await userEvent.clear(inputFields[1]);
    await userEvent.type(inputFields[1], "2800");

    // Click "Save"
    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    // Mock API response
    await MakingChargesAPI.updateMakingCharges(updatePayload);

    // Ensure API was called with correct data
    expect(MakingChargesAPI.updateMakingCharges).toHaveBeenCalledWith(updatePayload);

    // Wait for UI to reflect the updated values
    await waitFor(() => {
      expect(require("../../Utils/MakingChargesApi").updateMakingCharges).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            makingChargePerGram: 2600,
          }),
          expect.objectContaining({
            makingChargePerGram: 2800,
          }),
        ])
      );
    });
  });

  // 3rd Test Case: renders Add New Row button after data loads
  test("renders Add New Row button after data loads", async () => {
    render(<MakingCharges />);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /add new row/i })).toBeInTheDocument();
    });
  });

  // 4th Test Case: clicking 'Add New Row' displays input fields
  test("clicking 'Add New Row' displays input fields", async () => {
    render(<MakingCharges />);

    // Wait for the loader to disappear
    await waitFor(() => {
      expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
    });

    // Click the "Add New Row" button
    const addButton = await screen.findByRole("button", { name: /add new row/i });
    userEvent.click(addButton);

    // Verify input fields appear
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/Min Weight/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/Number or Above/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/Making Charge/i)).toBeInTheDocument();
    });
  });
  
});
