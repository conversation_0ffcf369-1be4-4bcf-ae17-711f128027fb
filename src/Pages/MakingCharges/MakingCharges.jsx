import React, { useEffect, useState } from "react";
import ConfirmModal from "../../Components/Modal/ConfirmModal";
import { Bars3Icon } from "@heroicons/react/24/outline";
import Loader from "../../Components/Loader/Loader";
import { useAuth } from "../../Context/AuthContext";
import {
  fetchMakingCharges,
  updateMakingCharges,
  addMakingCharge,
} from "../../Utils/MakingChargesApi";
import ActionButton from "../../Components/Button/ActionButton";
import MessageNotification from "../../Components/MessageNotification/MessageNotification";
import Pagination from "../../Components/Pagination/Pagination";
import DynamicRow from "../../Components/DynamicRow/DynamicRow";
import Header from "../../Components/Header/Header";

export default function MakingCharges() {
  const { toggleSidebar, isDesktop } = useAuth();
  const [makingCharges, setMakingCharges] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [updatedCharges, setUpdatedCharges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [previousData, setPreviousData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddingRow, setIsAddingRow] = useState(false);
  const itemsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await fetchMakingCharges();
        setMakingCharges(data);
      } catch {
        setErrorMessage(error.response?.data?.errors?.[0]?.message || "Failed to fetch making charges!");
      }
      finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleEditClick = () => {
    if (!isEditing) {
      setPreviousData([...makingCharges]);
    } else {
      setModalOpen(true);
    }
    setIsEditing(!isEditing);
  };

  const handleChargeChange = (id, field, value) => {
    setMakingCharges((prevData) =>
      prevData.map((item) =>
        item._id === id ? { ...item, [field]: value } : item
      )
    );

    setUpdatedCharges((prev) => {
      const existingCharge = prev.find((item) => item.id === id);
      if (existingCharge) {
        return prev.map((item) =>
          item.id === id ? { ...item, [field]: value } : item
        );
      }
      return [...prev, { id, [field]: value }];
    });
  };

  const handleError = (message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(""), 5000);
  };

  const handleConfirmChanges = async () => {
    setModalOpen(false);
    document.body.style.overflow = "auto";
    setButtonLoader(true);
  
    if (!Array.isArray(updatedCharges) || updatedCharges.length === 0) {
      setButtonLoader(false);
      return;
    }
  
    const previousDataCopy = JSON.parse(JSON.stringify(makingCharges)); // Deep clone
  
    const validUpdates = updatedCharges.map((charge) => {
      const fullItem = makingCharges.find((item) => item._id === charge.id);
      return {
        id: charge.id,
        minWeight: fullItem?.minWeight,
        maxWeight:
          charge.maxWeight?.toString().toLowerCase() === "above"
            ? null
            : parseFloat(charge.maxWeight ?? fullItem?.maxWeight),
        makingChargePerGram: parseFloat(
          charge.makingChargePerGram ?? fullItem?.makingChargePerGram
        ),
      };
    });
  
    try {
      const response = await updateMakingCharges(validUpdates);
  
      if (response.responseCode !== 0 || response.status !== "success") {
        throw new Error(response.message || "Update failed.");
      }
  
      setIsEditing(false);
      setUpdatedCharges([]);
      handleSuccess(response.message || "Making charges updated successfully!");
  
      // Fetch fresh data after success
      const refreshedData = await fetchMakingCharges();
      setMakingCharges(
        refreshedData.map((item) => ({
          ...item,
          maxWeight: item.maxWeight === null ? "Above" : item.maxWeight,
        }))
      );
    } catch (error) {
      console.error("Error occurred:", error);
      handleError(error.response?.data?.errors?.[0]?.message || "Failed to update making charges!");
  
      // **Force re-fetching the data instead of relying on previousDataCopy**
      try {
        const refreshedData = await fetchMakingCharges();
        setMakingCharges(
          refreshedData.map((item) => ({
            ...item,
            maxWeight: item.maxWeight === null ? "Above" : item.maxWeight,
          }))
        );
      } catch (fetchError) {
        console.error("Failed to re-fetch making charges:", fetchError);
        setMakingCharges(previousDataCopy); // If fetching fails, fall back to old data
      }
  
      setUpdatedCharges([]);
      setIsEditing(false);
    } finally {
      setButtonLoader(false);
      document.body.style.overflow = "auto";
    }
  };
  

  const handleCancelChanges = () => {
    setMakingCharges(previousData);
    setUpdatedCharges([]);
    setIsEditing(false);
    setModalOpen(false);
    setIsAddingRow(false);
    document.body.style.overflow = "auto";
  };

  const handleAddRow = async (newRowData) => {
    if (!newRowData) {
      handleError("Invalid row data.");
      return;
    }
  
    let { minWeight, maxWeight, makingChargePerGram } = newRowData;
  
    minWeight = Number(minWeight);
    makingChargePerGram = Number(makingChargePerGram);
    maxWeight = maxWeight.toLowerCase() === "above" ? null : Number(maxWeight);
  
    if (
      isNaN(minWeight) ||
      (maxWeight !== null && isNaN(maxWeight)) ||
      isNaN(makingChargePerGram)
    ) {
      handleError("Please enter valid numbers.");
      return;
    }
  
    if (maxWeight !== null && minWeight >= maxWeight) {
      handleError("Min Weight must be less than Max Weight.");
      return;
    }
  
    try {
      setLoading(true);
      const existingData = await fetchMakingCharges();
  
      if (existingData.length === 0) {
        // Allow adding first row with minWeight = 0
        if (minWeight !== 0) {
          handleError("First row must start with Min Weight of 0.");
          return;
        }
      } else {
        const lastRow = existingData[existingData.length - 1];
  
        if (lastRow?.maxWeight !== minWeight) {
          handleError(`Min Weight must match the last row's Max Weight.`);
          return;
        }
      }
  
      await addMakingCharge([{ minWeight, maxWeight, makingChargePerGram }]);
  
      const refreshedData = await fetchMakingCharges();
      setMakingCharges(refreshedData);
      handleSuccess("New row added successfully!");
    } catch (error) {
      handleError("Failed to add new row!");
    } finally {
      setLoading(false);
      setIsAddingRow(false);
    }
  };
  

  const handleSuccess = (message) => {
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(""), 5000);
  };

  // const totalPages = Math.ceil(makingCharges.length / itemsPerPage);
  // const currentItems = makingCharges.slice(
  //   (currentPage - 1) * itemsPerPage,
  //   currentPage * itemsPerPage
  // );

  return (
    <div className="flex lg:ml-72 flex-col">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="w-full h-[100vh] bg-white shadow-sm p-6">
            <h1 className="text-4xl font-bold text-gray-800 mb-6">
              Making Charges
            </h1>

            <MessageNotification message={successMessage} type="success" />
            <MessageNotification message={errorMessage} type="error" />

            <div className="flex justify-end mt-6 pb-8">
              <ActionButton
                onClick={handleEditClick}
                onDiscard={handleCancelChanges}
                isLoading={buttonLoader}
                isEditing={isEditing}
                text="Making Charges"
              />
            </div>

            <div className="overflow-x-auto flex justify-center">
              <table className="w-[70%] min-w-[600px] divide-y divide-gray-300 border border-gray-300 shadow-md">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-6 py-3 text-center">Metal Range (g)</th>
                    <th className="px-6 py-3 text-center bg-yellow-100">
                      Making Charge (₹/gm)
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white text-center">
                  {makingCharges.map((item) => (
                    <tr key={item._id}>
                      {/* Editable Metal Range */}
                      <td className="p-4 text-center">
                        {isEditing ? (
                          <>
                            <input
                              type="number"
                              className="w-20 p-2 border rounded-md text-center mr-2"
                              value={item.minWeight}
                              placeholder="number"
                              onChange={(e) =>
                                handleChargeChange(
                                  item._id,
                                  "minWeight",
                                  e.target.value
                                )
                              }
                            />
                            -
                            <input
                              type="text"
                              className="w-20 p-2 border rounded-md text-center ml-2"
                              value={item.maxWeight ?? ""}
                              placeholder="above or number"
                              onChange={(e) =>
                                handleChargeChange(
                                  item._id,
                                  "maxWeight",
                                  e.target.value
                                )
                              }
                            />
                          </>
                        ) : (
                          <span>
                            {item.minWeight} - {item.maxWeight ?? "Above"}
                          </span>
                        )}
                      </td>

                      {/* Editable Making Charge */}
                      <td className="p-4 bg-yellow-50 text-center">
                        {isEditing ? (
                          <input
                            type="number"
                            className="w-20 p-2 border rounded-md text-center"
                            value={item.makingChargePerGram}
                            onChange={(e) =>
                              handleChargeChange(
                                item._id,
                                "makingChargePerGram",
                                e.target.value
                              )
                            }
                          />
                        ) : (
                          <span>₹{item.makingChargePerGram}</span>
                        )}
                      </td>
                    </tr>
                  ))}
                  {isAddingRow && (
                    <tr>
                      <td colSpan="5" className="p-4 text-center">
                        <DynamicRow
                          columns={[
                            {
                              key: "minWeight",
                              placeholder: "Min Weight",
                              type: "number",
                            },
                            {
                              key: "maxWeight",
                              placeholder: "Number or Above",
                              type: "text",
                            },
                            {
                              key: "makingChargePerGram",
                              placeholder: "Making Charge",
                              type: "number",
                            },
                          ]}
                          onAddRow={handleAddRow}
                          onCancel={() => setIsAddingRow(false)}
                        />
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            <div className="flex justify-center mt-6 pb-8">
              <button
                className="cursor-pointer p-2 border-gray-400 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 transition"
                onClick={() => setIsAddingRow(true)}
              >
                Add New Row
              </button>
            </div>

            {makingCharges.length > 10 && (
              <Pagination
                totalItems={makingCharges.length}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
              />
            )}
          </div>
          <ConfirmModal
            isOpen={modalOpen}
            onConfirm={handleConfirmChanges}
            onClose={handleCancelChanges}
          />
        </>
      )}
    </div>
  );
}
