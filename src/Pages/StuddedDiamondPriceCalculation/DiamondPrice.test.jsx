import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import DiamondPrice from './DiamondPrice';
import { useAuth } from "../../Context/AuthContext";

// Mock useAuth to prevent undefined errors
jest.mock("../../Context/AuthContext", () => ({
  useAuth: jest.fn(),
}));

beforeEach(() => {
  useAuth.mockReturnValue({
    toggleSidebar: jest.fn(),
    isDesktop: true,
  });
});

describe("MetalPriceCalculation Component", () => {
  test("renders correctly and loads data", async () => {
    render(<DiamondPrice />);

    await waitFor(() => {
      expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
    });
  });
});
