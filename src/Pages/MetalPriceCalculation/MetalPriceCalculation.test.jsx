import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import MetalPriceCalculation from "./MetalPriceCalculation";
import { useAuth } from "../../Context/AuthContext";
import userEvent from "@testing-library/user-event";
import MetalPriceAPI from "../../Utils/MetalPriceCalculationApi"

// Mock useAuth to prevent undefined errors
jest.mock("../../Context/AuthContext", () => ({
  useAuth: jest.fn(),
}));

// Api testing
jest.mock("../../Utils/MetalPriceCalculationApi", () => ({
  getMetalPrices: jest.fn(() =>
    Promise.resolve([
      { _id: "1", metalName: "Gold", metalPurity: "24K", fetchedPrice: 5000, handlingCharge: 10 },
      { _id: "2", metalName: "Platinum", metalPurity: "999", fetchedPrice: 1000, handlingCharge: 5 }
    ])
  ),
  updateHandlingCharges: jest.fn(() => Promise.resolve({ success: true })),
  addMetalPrice: jest.fn(() => Promise.resolve({ success: true }))
}));

beforeEach(() => {
  useAuth.mockReturnValue({
    toggleSidebar: jest.fn(),
    isDesktop: true,
  });
});

describe("MetalPriceCalculation Component", () => {

  // 1st Testcase For fetching the data
  test("renders correctly and loads data", async () => {
    render(<MetalPriceCalculation />);

    // Wait for the loader to disappear
    await waitFor(() => {
      expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
    });

    // Check if table headers are displayed
    expect(screen.getByText(/Metal Price Calculation/i)).toBeInTheDocument();
    expect(screen.getByText("Gold")).toBeInTheDocument();
    expect(screen.getByText("Platinum")).toBeInTheDocument();
  });

  // 2nd Testcase for cheking the handeling charges
  test("updates handling charges and calls API", async () => {
  render(<MetalPriceCalculation />);

  // Wait for data to load
  await waitFor(() => {
    expect(screen.getByText("Gold")).toBeInTheDocument();
  });

  // Click "Edit Handling Charges"
  userEvent.click(screen.getByRole("button", { name: /edit handling charges/i }));

  // Wait for input fields to appear
  await waitFor(() => {
    expect(screen.getAllByRole("spinbutton").length).toBeGreaterThan(0);
  });

  // Update the handling charge for Gold
  const handlingChargeInput = screen.getAllByRole("spinbutton")[0];
  userEvent.clear(handlingChargeInput);
  userEvent.type(handlingChargeInput, "15");

  // Wait for React state to update before proceeding
  await waitFor(() => {
    expect(handlingChargeInput).toHaveValue(15);
  });

  // Click "Save"
  userEvent.click(screen.getByRole("button", { name: /save/i }));

  // Wait for confirmation modal to appear
  await waitFor(() => {
    expect(screen.getByText(/confirm changes/i)).toBeInTheDocument();
  });

  // Click "Confirm" in modal
  userEvent.click(screen.getByRole("button", { name: /confirm/i }));

  // Wait for state to update before checking the API call
  await waitFor(() => {
    expect(require("../../Utils/MetalPriceCalculationApi").updateHandlingCharges).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          handlingCharge: 15,
        }),
      ])
    );
  });
});

// 3rd Testcase for Add new row FUnctionality

// checking for add new row button
test("renders Add New Row button after data loads", async () => {
  render(<MetalPriceCalculation />);

  await waitFor(() => {
    expect(screen.getByRole("button", { name: /add new row/i })).toBeInTheDocument();
  });
});

test("clicking 'Add New Row' displays input fields", async () => {
  render(<MetalPriceCalculation />);

  // Wait for the loader to disappear
  await waitFor(() => {
    expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
  });

  // Now click the "Add New Row" button
  const addButton = await screen.findByRole("button", { name: /add new row/i });
  userEvent.click(addButton);

  // Verify input fields appear
  await waitFor(() => {
    expect(screen.getByPlaceholderText(/Enter Gold or Platinum/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/Enter Metal Purity/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/Enter Handling Charge/i)).toBeInTheDocument();
  });
});

// 5th Testcase: Adding a new metal price
test("adds a new metal price and calls API", async () => {
  // Render the component
  render(<MetalPriceCalculation />);

  // Wait for the Add New Row button to be available
  const addNewRowButton = await screen.findByRole("button", { name: /add new row/i });
  fireEvent.click(addNewRowButton);

  // Fill in metal details
  await userEvent.type(screen.getByPlaceholderText(/Enter Gold or Platinum/i), "Platinum");
  await userEvent.type(screen.getByPlaceholderText(/Enter Metal Purity/i), "P950");
  await userEvent.type(screen.getByPlaceholderText(/Enter Handling Charge/i), "20");

  // Click on the Add button
  const addButton = screen.getByRole("button", { name: /^Add$/i });
  fireEvent.click(addButton);

  // Wait for the API call to be triggered
  await waitFor(() => {
    expect(MetalPriceAPI.addMetalPrice).toHaveBeenCalledTimes(1);
  });

  expect(MetalPriceAPI.addMetalPrice).toHaveBeenCalledWith({
    metalName: "Platinum",
    metalPurity: "P950",
    handlingCharge: "20", 
  });
});  

});
