import React, { useState, useEffect } from "react";
import { useAuth } from "../../Context/AuthContext";
import ActionButton from "../../Components/Button/ActionButton";
import MessageNotification from "../../Components/MessageNotification/MessageNotification";
import Loader from "../../Components/Loader/Loader";
import { fetchPartialPaymentConfig, updatePartialPaymentConfig } from "../../Utils/MinimumChargesApi";

export default function MinimumCharges() {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [error, setError] = useState(null);
  const [charges, setCharges] = useState({
    metal: 0,
    making: 0,
    studdedDiamonds: 0,
    solitaire: 0,
    gemstones: 0
  });
  const [previousCharges, setPreviousCharges] = useState({});
  const [validationErrors, setValidationErrors] = useState({});

  const fetchCharges = async () => {
    setLoading(true);
    try {
      const response = await fetchPartialPaymentConfig();
      if (response.status === "success" && response.data.length > 0) {
        const configData = response.data[0];
        const { metal, making, studdedDiamonds, solitaire, gemstones } = configData;
        setCharges({
          metal,
          making,
          studdedDiamonds,
          solitaire,
          gemstones
        });
      }
    } catch (error) {
      setError("Failed to load charges: " + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCharges();
  }, []);

  const handleEditClick = async () => {
    if (!isEditing) {
      setPreviousCharges({ ...charges });
      setValidationErrors({});
      setIsEditing(true);
    } else {
      // Check for validation errors before saving
      const hasErrors = Object.values(validationErrors).some(error => error);
      if (hasErrors) {
        setError("Please fix validation errors before saving.");
        setTimeout(() => setError(null), 5000);
        return;
      }

      setButtonLoader(true);
      try {
        const response = await updatePartialPaymentConfig(charges);
        if (response.status === "success") {
          setSuccessMessage(response.message || "Charges updated successfully!");
          setIsEditing(false);
          setValidationErrors({});
        } else {
          throw new Error(response.message || "Failed to update charges");
        }
      } catch (error) {
        setError("Failed to update charges: " + (error.response?.data?.message || error.message));
      } finally {
        setButtonLoader(false);
        setTimeout(() => {
          setSuccessMessage("");
          setError(null);
        }, 5000);
      }
    }
  };

  const handleChargeChange = (field, value) => {
    const numValue = value === '' ? '' : parseInt(value) || 0;

    // Validate the value
    let hasError = false;
    if (numValue < 0) {
      hasError = true;
    } else if (numValue > 100) {
      hasError = true;
    }

    setValidationErrors(prev => ({
      ...prev,
      [field]: hasError
    }));

    setCharges(prev => ({
      ...prev,
      [field]: numValue
    }));
  };

  const handleCancelChanges = () => {
    setCharges(previousCharges);
    setValidationErrors({});
    setIsEditing(false);
  };

  if (loading) return <Loader />;

  return (
    <div className="flex lg:ml-72 flex-col p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="font-bold text-gray-800" style={{ fontSize: '26px' }}>
          Minimum Charges to be Charged for Partial Payment
        </h1>
        <ActionButton
          onClick={handleEditClick}
          onDiscard={handleCancelChanges}
          isLoading={buttonLoader}
          isEditing={isEditing}
          text="Percentages"
        />
      </div>

      {successMessage && (
        <MessageNotification message={successMessage} type="success" />
      )}
      {error && <MessageNotification message={error} type="error" />}

      <div className="flex justify-center">
        <table className="w-[70%] min-w-[600px] divide-y divide-gray-300 border border-gray-300 shadow-md">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-center">Material</th>
              <th className="px-6 py-3 text-center bg-yellow-100">Percentage (%)</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {Object.entries(charges).map(([key, value]) => (
              <tr key={key}>
                <td className="p-4 text-left font-medium">
                  {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                </td>
                <td className="p-4 text-center bg-yellow-50">
                  {isEditing ? (
                    <input
                      type="number"
                      value={value === 0 ? '' : value}
                      onChange={(e) => handleChargeChange(key, e.target.value)}
                      className={`w-20 p-2 border rounded-md text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${
                        validationErrors[key] ? 'border-red-500 bg-red-50' : 'border-gray-300'
                      }`}
                      min="0"
                      max="100"
                      step="1"
                    />
                  ) : (
                    `${value}%`
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
} 