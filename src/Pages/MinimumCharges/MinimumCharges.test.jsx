import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import MinimumCharges from "./MinimumCharges";
import { useAuth } from "../../Context/AuthContext";
import userEvent from "@testing-library/user-event";

// Mock useAuth
jest.mock("../../Context/AuthContext", () => ({
  useAuth: jest.fn(),
}));

// Mock API calls
jest.mock("../../Utils/MinimumChargesApi", () => ({
  fetchMinimumCharges: jest.fn(() =>
    Promise.resolve({
      metal: 0.5,
      making: 1.0,
      studdedDiamonds: 0.5,
      solitaire: 0.8,
      gemstones: 1.0,
    })
  ),
  updateMinimumCharges: jest.fn(() => Promise.resolve({ success: true })),
}));

describe("MinimumCharges Component", () => {
  beforeEach(() => {
    useAuth.mockReturnValue({
      toggleSidebar: jest.fn(),
      isDesktop: true,
    });
  });

  test("renders component with initial charges", async () => {
    render(<MinimumCharges />);

    // Check if title is rendered
    expect(screen.getByText("Minimum Charges to be Charged for Partial Payment")).toBeInTheDocument();

    // Check if all materials are rendered
    expect(screen.getByText("Metal")).toBeInTheDocument();
    expect(screen.getByText("Making")).toBeInTheDocument();
    expect(screen.getByText("Studded Diamonds")).toBeInTheDocument();
    expect(screen.getByText("Solitaire")).toBeInTheDocument();
    expect(screen.getByText("Gemstones")).toBeInTheDocument();

    // Check if initial percentages are rendered
    expect(screen.getByText("50%")).toBeInTheDocument();
    expect(screen.getByText("100%")).toBeInTheDocument();
    expect(screen.getByText("80%")).toBeInTheDocument();
  });

  test("enables editing mode when edit button is clicked", async () => {
    render(<MinimumCharges />);

    // Click edit button
    const editButton = screen.getByText("Edit Percentages");
    fireEvent.click(editButton);

    // Check if input fields are rendered
    const inputs = screen.getAllByRole("spinbutton");
    expect(inputs).toHaveLength(5); // One for each material

    // Check if inputs have correct initial values
    const metalInput = inputs[0];
    expect(metalInput).toHaveValue(0.5);
  });

  test("updates values when editing", async () => {
    render(<MinimumCharges />);

    // Enter edit mode
    const editButton = screen.getByText("Edit Percentages");
    fireEvent.click(editButton);

    // Get first input and change its value
    const inputs = screen.getAllByRole("spinbutton");
    const metalInput = inputs[0];
    
    fireEvent.change(metalInput, { target: { value: "0.6" } });
    expect(metalInput).toHaveValue(0.6);
  });

  test("shows confirmation modal when saving changes", async () => {
    render(<MinimumCharges />);

    // Enter edit mode
    const editButton = screen.getByText("Edit Percentages");
    fireEvent.click(editButton);

    // Make a change
    const inputs = screen.getAllByRole("spinbutton");
    fireEvent.change(inputs[0], { target: { value: "0.6" } });

    // Try to save (click edit button again)
    fireEvent.click(editButton);

    // Check if confirmation modal is shown
    expect(screen.getByText("Are you sure you want to save these changes?")).toBeInTheDocument();
  });

  test("cancels changes when cancel is clicked", async () => {
    render(<MinimumCharges />);

    // Enter edit mode
    const editButton = screen.getByText("Edit Percentages");
    fireEvent.click(editButton);

    // Make a change
    const inputs = screen.getAllByRole("spinbutton");
    fireEvent.change(inputs[0], { target: { value: "0.6" } });

    // Click cancel
    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    // Check if value reverted
    expect(screen.getByText("50%")).toBeInTheDocument();
  });

  test("validates input values", async () => {
    render(<MinimumCharges />);

    // Enter edit mode
    const editButton = screen.getByText("Edit Percentages");
    fireEvent.click(editButton);

    // Try to enter invalid values
    const inputs = screen.getAllByRole("spinbutton");
    const metalInput = inputs[0];
    
    fireEvent.change(metalInput, { target: { value: "-0.1" } });
    expect(metalInput).toHaveValue(0); // Should be clamped to min

    fireEvent.change(metalInput, { target: { value: "1.5" } });
    expect(metalInput).toHaveValue(1); // Should be clamped to max
  });
}); 