import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "../../Context/AuthContext";
import MetalPriceCalculation from "../MetalPriceCalculation/MetalPriceCalculation";
import MakingCharges from "../MakingCharges/MakingCharges";
import DiamondPrice from "../StuddedDiamondPriceCalculation/DiamondPrice";
import SolitairePrice from '../SolitairePriceCalculation/SolitairePrice';
import GemstonePrice from '../GemstonePriceCalculation/GemstonePrice';
import MinimumCharges from '../MinimumCharges/MinimumCharges';
import Login from "../Login/Login";
import ProtectedRoute from "../../Components/ProtectedRoute/ProtectedRoute";
import ProtectedLayout from "../../Components/ProtectedLayout/ProtectedLayout";
import Loader from "../../Components/Loader/Loader";
import NotFound from "../NotFound/NotFound";

const Layout = () => {
    const { isLoggedIn, isLoading } = useAuth();

    // Show loading spinner while checking authentication
    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <Loader />
            </div>
        );
    }

    return (
        <Routes>
            {/* Public route - Login */}
            <Route
                path="/login"
                element={
                    isLoggedIn ? <Navigate to="/metal-price" replace /> : <Login />
                }
            />

            {/* Root redirect */}
            <Route
                path="/"
                element={
                    <ProtectedRoute>
                        <Navigate to="/metal-price" replace />
                    </ProtectedRoute>
                }
            />

            {/* Protected routes */}
            <Route
                path="/metal-price"
                element={
                    <ProtectedLayout>
                        <MetalPriceCalculation />
                    </ProtectedLayout>
                }
            />

            <Route
                path="/making-charges"
                element={
                    <ProtectedLayout>
                        <MakingCharges />
                    </ProtectedLayout>
                }
            />

            <Route
                path="/diamond-price"
                element={
                    <ProtectedLayout>
                        <DiamondPrice />
                    </ProtectedLayout>
                }
            />

            <Route
                path="/solitaire-price"
                element={
                    <ProtectedLayout>
                        <SolitairePrice />
                    </ProtectedLayout>
                }
            />

            <Route
                path="/gemstone-price"
                element={
                    <ProtectedLayout>
                        <GemstonePrice />
                    </ProtectedLayout>
                }
            />

            <Route
                path="/minimum-charges"
                element={
                    <ProtectedLayout>
                        <MinimumCharges />
                    </ProtectedLayout>
                }
            />

            {/* 404 Route - Public, no authentication required */}
            <Route path="*" element={<NotFound />} />
        </Routes>
    );
};

export default Layout;
