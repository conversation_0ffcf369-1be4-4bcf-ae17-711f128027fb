import React, { useEffect, useState } from "react";
import ConfirmModal from "../../Components/Modal/ConfirmModal";
import { useAuth } from "../../Context/AuthContext";
import Loader from "../../Components/Loader/Loader";
import ActionButton from "../../Components/Button/ActionButton";
import MessageNotification from "../../Components/MessageNotification/MessageNotification";
import GemstonePriceTable from "../../Components/Tables/GemstoneTable/GemstonePriceTable";
import {
  fetchSmallStudded,
  fetchSemiPreciousStudded,
  fetchPreciousStudded,
  updateSmallStudded,
  updateSemiPreciousStudded,
  updatePreciousStudded,
  addRowSmallStudded,
  addRowSemiPreciousStudded,
  addRowPreciousStudded,
} from "../../Utils/GemstonePriceApi";

export default function GemstonePrice() {
  const { isSidebarOpen, toggleSidebar, isDesktop } = useAuth();
  const [gemstonePrices1, setGemstonePrices1] = useState([]);
  const [gemstonePrices2, setGemstonePrices2] = useState([]);
  const [gemstonePrices3, setGemstonePrices3] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [buttonLoader, setButtonLoader] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [previousData1, setPreviousData1] = useState([]);
  const [previousData2, setPreviousData2] = useState([]);
  const [previousData3, setPreviousData3] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setErrorMessage("");

      try {
        const [data1, data2, data3] = await Promise.all([
          fetchSmallStudded(),
          fetchSemiPreciousStudded(),
          fetchPreciousStudded(),
        ]);

        setGemstonePrices1(data1);
        setGemstonePrices2(data2);
        setGemstonePrices3(data3);
      } catch (error) {
        handleError(error, "Failed to fetch gemstone prices.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleEditClick = () => {
    if (!isEditing) {
      setPreviousData1([...gemstonePrices1]);
      setPreviousData2([...gemstonePrices2]); 
      setPreviousData3([...gemstonePrices3]);
      setIsEditing(true);
    } else {
      setModalOpen(true);
    }
    setIsEditing(!isEditing);
  };

  const handleConfirmChanges = async () => {
    setModalOpen(false);
    document.body.style.overflow = "auto";
    setButtonLoader(true);
  
    const formatData = (data) =>
      data.map(({ _id, ratePerCt }) => ({
        id: _id, 
        ratePerCt,
      }));
  
    const formattedGemstonePrices1 = formatData(gemstonePrices1);
    const formattedGemstonePrices2 = formatData(gemstonePrices2);
    const formattedGemstonePrices3 = formatData(gemstonePrices3);

    try {
      await Promise.all([
        updateSmallStudded(formattedGemstonePrices1),
        updateSemiPreciousStudded(formattedGemstonePrices2),
        updatePreciousStudded(formattedGemstonePrices3),
      ]);
  
      // Fetch updated data after successful update
      const [updated1, updated2, updated3] = await Promise.all([
        fetchSmallStudded(),
        fetchSemiPreciousStudded(),
        fetchPreciousStudded(),
      ]);
  
      setGemstonePrices1(updated1);
      setGemstonePrices2(updated2);
      setGemstonePrices3(updated3);
  
      setIsEditing(false);
      handleSuccess("Gemstone prices updated successfully!");
    } catch (error) {
      handleError(error, "Failed to update gemstone prices.");
    } finally {
      setButtonLoader(false);
      document.body.style.overflow = "auto";
    }
  };
  
  

  const handlePriceChange = (id, newValue, setGemstonePrices) => {
    setGemstonePrices((prev) =>
      prev.map((item) =>
        item._id === id ? { ...item, ratePerCt: newValue } : item
      )
    );
  };
  
  
  const handleAddRow = async (newRow, category) => {
    let addRowFunction;
    let setGemstonePrices;

    switch (category) {
      case "smallStudded":
        addRowFunction = addRowSmallStudded;
        setGemstonePrices = setGemstonePrices1;
        break;
      case "semiPrecious":
        addRowFunction = addRowSemiPreciousStudded;
        setGemstonePrices = setGemstonePrices2;
        break;
      case "precious":
        addRowFunction = addRowPreciousStudded;
        setGemstonePrices = setGemstonePrices3;
        break;
      default:
        return;
    }

    // Ensure the first letter of "quality" is capitalized
    if (newRow.quality) {
      newRow.quality =
        newRow.quality.charAt(0).toUpperCase() + newRow.quality.slice(1);
    }

    try {
      const response = await addRowFunction(newRow);

      if (response?.data?.status === "success") {
        setGemstonePrices((prev) => [...prev, response.data.data]);
        setSuccessMessage(
          response?.data?.message || "New row added successfully!"
        );
        setTimeout(() => setSuccessMessage(""), 5000);
      } else {
        setError(true);
        setSuccessMessage(response?.data?.message || "Failed to add new row.");
        setTimeout(() => setSuccessMessage(""), 5000);
      }
    } catch (error) {
      console.error("Error adding row:", error);
      setError(true);
      setSuccessMessage("An error occurred while adding a new row.");
      setTimeout(() => setSuccessMessage(""), 5000);
    }
  };

  const handleSuccess = (message) => {
    setSuccessMessage(capitalizeFirstLetter(message));
    setTimeout(() => setSuccessMessage(""), 5000);
  };

  const handleError = (error, fallbackMessage) => {
    console.error("API Error:", error);
    const errorMsg =
      error?.response?.data?.message || error?.message || fallbackMessage;
    setErrorMessage(capitalizeFirstLetter(errorMsg));
    setTimeout(() => setErrorMessage(""), 5000);
  };

  const capitalizeFirstLetter = (text) => {
    return text.charAt(0).toUpperCase() + text.slice(1);
  };

  const handleCancelChanges = () => {
    setGemstonePrices1(previousData1);
    setGemstonePrices2(previousData2);
    setGemstonePrices3(previousData3);
    setIsEditing(false);
    setModalOpen(false);
  };

  return (
    <div className="flex lg:ml-72 flex-col">
      {loading ? (
        <Loader />
      ) : (
        <>
          <div className="w-full h-auto bg-white shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="font-bold text-gray-800" style={{ fontSize: '26px' }}>
                Gemstone Prices
              </h1>
              <ActionButton
                onClick={handleEditClick}
                onDiscard={handleCancelChanges}
                isLoading={buttonLoader}
                isEditing={isEditing}
                text="Prices"
              />
            </div>

            {successMessage && (
              <MessageNotification message={successMessage} type="success" />
            )}
            {errorMessage && (
              <MessageNotification message={errorMessage} type="error" />
            )}

            <div className="space-y-14 pb-8">
              <GemstonePriceTable
                tableHeading="Small Studded Gemstone (< 0.3 carat)"
                gemstonePrices={gemstonePrices1}
                setGemstonePrices={setGemstonePrices1}
                isEditing={isEditing}
                category="smallStudded"
                onAddRow={(newRow) => handleAddRow(newRow, "smallStudded")}
                handlePriceChange={handlePriceChange}
              />

              <GemstonePriceTable
                tableHeading="Semi-Precious Studded (> 0.3 carat)"
                gemstonePrices={gemstonePrices2}
                setGemstonePrices={setGemstonePrices2}
                isEditing={isEditing}
                category="semiPrecious"
                onAddRow={(newRow) => handleAddRow(newRow, "semiPrecious")}
                handlePriceChange={handlePriceChange}
              />

              <GemstonePriceTable
                tableHeading="Precious Studded (> 0.3 carat)"
                gemstonePrices={gemstonePrices3}
                setGemstonePrices={setGemstonePrices3}
                isEditing={isEditing}
                category="precious"
                onAddRow={(newRow) => handleAddRow(newRow, "precious")}
                handlePriceChange={handlePriceChange}
              />
            </div>
          </div>

          <ConfirmModal
            isOpen={modalOpen}
            onConfirm={handleConfirmChanges}
            onClose={() => setModalOpen(false)}
          />
        </>
      )}
    </div>
  );
}
