import React from "react";
import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import GemstonePrice from './GemstonePrice';
import { useAuth } from "../../Context/AuthContext";

// Mock useAuth to prevent undefined errors
jest.mock("../../Context/AuthContext", () => ({
  useAuth: jest.fn(),
}));

beforeEach(() => {
  useAuth.mockReturnValue({
    toggleSidebar: jest.fn(),
    isDesktop: true,
  });
});

describe("MetalPriceCalculation Component", () => {
  test("renders correctly and loads data", async () => {
    render(<GemstonePrice />);

    await waitFor(() => {
      expect(screen.queryByRole("progressbar")).not.toBeInTheDocument();
    });
  });
});
