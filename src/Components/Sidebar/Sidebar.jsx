import { useAuth } from "../../Context/AuthContext";
import { useLocation, useNavigate } from "react-router-dom";
import logo from "../../assets/logo.webp";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  TransitionChild,
} from "@headlessui/react";
import {
  XMarkIcon,
  DocumentDuplicateIcon,
  ChevronDownIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";
import { useState, useEffect } from "react";

const navigation = [
  { name: "Metal Price Calculation", href: "/metal-price" },
  { name: "Making Charges", href: "/making-charges" },
  { name: "Studded Diamond Price Calculation", href: "/diamond-price" },
  { name: "Solitaire Price Calculation", href: "/solitaire-price" },
  { name: "Gemstone Price Calculation", href: "/gemstone-price" },
  { name: "Minimum Charges", href: "/minimum-charges" }
];

export default function Sidebar() {
  const { isSidebarOpen, toggleSidebar, isDesktop } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const [selectedNav, setSelectedNav] = useState(null);
  const [isDropdownOpen, setDropdownOpen] = useState(true);

  useEffect(() => {
    const currentNav = navigation.find((nav) => nav.href === location.pathname);
    if (currentNav) {
      setSelectedNav(currentNav);
    } else {
      setSelectedNav(navigation[0]);
      navigate(navigation[0].href);
    }
  }, [location.pathname]);

  const handleNavChange = (href) => {
    setSelectedNav(navigation.find((nav) => nav.href === href));
    navigate(href);
    if (!isDesktop) toggleSidebar();
  };

  return (
    <div>
      <Dialog
        open={isSidebarOpen}
        onClose={() => {
          toggleSidebar();
          setDropdownOpen(false);
        }}
        className="relative z-50 lg:hidden"
      >
        <DialogBackdrop className="fixed inset-0 bg-gray-300/80 transition-opacity" />
        <div className="fixed inset-0 flex">
          <DialogPanel className="relative w-full max-w-xs flex-1 transform transition duration-300 ease-in-out shadow-lg bg-gray-100 p-4">
            <TransitionChild>
              <div className="absolute top-0 left-full flex w-16 justify-center pt-5">
                <button
                  onClick={() => {
                    toggleSidebar();
                    setDropdownOpen(false);
                  }}
                  className="p-2.5 cursor-pointer"
                >
                  <XMarkIcon className="size-6 text-black" />
                </button>
              </div>
            </TransitionChild>
            <div className="flex flex-col gap-y-6 overflow-y-auto">
              <div className="flex h-16 items-center">
                <img
                  src={logo}
                  alt="Diamond HQ Logo"
                  className="h-12 w-auto mr-3"
                />
              </div>
              <nav className="flex flex-col gap-4">
                <button
                  className="w-full flex cursor-pointer items-center justify-between p-3 bg-gray-200 text-black rounded-lg"
                  onClick={() => setDropdownOpen(!isDropdownOpen)}
                >
                  <span className="font-semibold">Dynamic Pricing</span>
                  {isDropdownOpen ? (
                    <ChevronDownIcon className="h-5 w-5" />
                  ) : (
                    <ChevronRightIcon className="h-5 w-5" />
                  )}
                </button>
                {isDropdownOpen && (
                  <div className="mt-2 pl-4 border-l-2 border-gray-500">
                    {navigation.map((item) => (
                      <button
                        key={item.name}
                        onClick={() => handleNavChange(item.href)}
                        className={`flex items-center cursor-pointer w-full text-left p-3 hover:bg-gray-200 rounded-md transition-all ${
                          selectedNav?.href === item.href
                            ? "font-bold text-black"
                            : "text-gray-600"
                        }`}
                      >
                        {item.name}
                      </button>
                    ))}
                  </div>
                )}
              </nav>
            </div>
          </DialogPanel>
        </div>
      </Dialog>

      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col bg-gray-100 p-6">
        <div className="flex flex-col gap-y-6">
          <div className="flex h-16 items-center">
            <img
              src={logo}
              alt="Diamond HQ Logo"
              className="h-12 w-auto mr-3"
            />
          </div>
          <nav className="flex flex-col gap-4">
            <button
              className="w-full flex cursor-pointer items-center justify-between p-3 bg-gray-200 text-black rounded-lg"
              onClick={() => setDropdownOpen(!isDropdownOpen)}
            >
              <span className="font-semibold">Dynamic Pricing</span>
              {isDropdownOpen ? (
                <ChevronDownIcon className="h-5 w-5" />
              ) : (
                <ChevronRightIcon className="h-5 w-5" />
              )}
            </button>
            {isDropdownOpen && (
              <div className="mt-2 pl-4 border-l-2 border-gray-500">
                {navigation.map((item) => (
                  <button
                    key={item.name}
                    onClick={() => handleNavChange(item.href)}
                    className={`block w-full text-left cursor-pointer p-3 hover:bg-gray-200 rounded-md transition-all ${
                      selectedNav?.href === item.href
                        ? "font-bold text-black"
                        : "text-gray-600"
                    }`}
                  >
                    {item.name}
                  </button>
                ))}
              </div>
            )}
          </nav>
        </div>
      </div>
    </div>
  );
}
