import React from "react";
import { useAuth } from "../../Context/AuthContext";
import Sidebar from "../Sidebar/Sidebar";
import Header from "../Header/Header";
import ProtectedRoute from "../ProtectedRoute/ProtectedRoute";

const ProtectedLayout = ({ children }) => {
    const { isSidebarOpen } = useAuth();

    return (
        <ProtectedRoute>
            <div className="flex h-screen overflow-hidden">
                <div className={`fixed top-0 left-0 h-full transition-all duration-300 ${isSidebarOpen ? "w-72" : "w-0"}`}>
                    <Sidebar />
                </div>
                <div
                    className={`flex-grow flex flex-col min-h-screen w-full transition-all duration-300 ${
                        isSidebarOpen ? "lg:ml-72" : "lg:ml-0"
                    }`}
                >
                    <Header />
                    <div className="flex-1 overflow-y-auto">
                        {children}
                    </div>
                </div>
            </div>
        </ProtectedRoute>
    );
};

export default ProtectedLayout;
