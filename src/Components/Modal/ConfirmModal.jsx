import React, { useEffect } from "react";

const ConfirmationModal = ({
    isOpen,
    onClose,
    onConfirm,
    title = "Confirm Changes",
    message = "Are you sure you want to save the changes?",
    confirmText = "Confirm",
    cancelText = "Cancel"
}) => {
    if (!isOpen) return null;

    // Disable scrolling when modal is open
    useEffect(() => {
        document.body.style.overflow = "hidden";
        return () => {
            document.body.style.overflow = "auto";
        };
    }, []);

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-opacity-10 backdrop-blur-sm">
            <div className="bg-white bg-opacity-95 p-6 rounded-lg shadow-xl w-96 border border-gray-300">
                <h2 className="text-xl font-semibold mb-4 text-center">{title}</h2>
                <p className="text-gray-700 text-center mb-6">{message}</p>
                <div className="flex justify-center gap-4">
                    <button
                        onClick={onConfirm}
                        className="px-6 py-2 cursor-pointer bg-green-500 text-white rounded-lg hover:bg-green-600 transition"
                    >
                        {confirmText}
                    </button>
                    <button
                        onClick={onClose}
                        className="px-6 py-2 cursor-pointer bg-gray-300 rounded-lg hover:bg-gray-400 transition"
                    >
                        {cancelText}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmationModal;
