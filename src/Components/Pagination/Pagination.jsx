import React from 'react';

const Pagination = ({ totalItems, itemsPerPage, currentPage, setCurrentPage }) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  return (
    <div className="flex justify-center mt-6 pb-8">
      <button
        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
        disabled={currentPage === 1}
        className={`px-4 py-2 mx-1 cursor-pointer rounded-md ${currentPage === 1 ? 'bg-gray-200 cursor-not-allowed' : 'bg-gray-300 hover:bg-gray-400'}`}
      >
        Previous
      </button>

      {[...Array(totalPages)].map((_, page) => (
        <button
          key={page}
          onClick={() => setCurrentPage(page + 1)}
          className={`px-4 py-2 mx-1 cursor-pointer rounded-md ${currentPage === page + 1 ? 'bg-gray-500 text-white' : 'bg-gray-300 hover:bg-gray-400'}`}
        >
          {page + 1}
        </button>
      ))}

      <button
        onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
        disabled={currentPage === totalPages}
        className={`px-4 py-2 mx-1 cursor-pointer rounded-md ${currentPage === totalPages ? 'bg-gray-200 cursor-not-allowed' : 'bg-gray-300 hover:bg-gray-400'}`}
      >
        Next
      </button>
    </div>
  );
};

export default Pagination;
