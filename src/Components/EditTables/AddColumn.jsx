import React, { useState } from "react";

export default function AddColumn({ 
  diamondTypes, 
  onAdd, 
  onCancel, 
  isGradeMode = false,  
  gradeRows = []      
}) {
  const [minSize, setMinSize] = useState("");
  const [maxSize, setMaxSize] = useState("");
  const [prices, setPrices] = useState({});
  const [gradeName, setGradeName] = useState(""); 
  

  // Handle price input change
  const handlePriceChange = (index, value) => {
    setPrices((prev) => ({
      ...prev,
      [index]: parseFloat(value) || 0,
    }));
  };

  // Handle form submission
  const handleSubmit = () => {
    if (isGradeMode) {
      if (!gradeName.trim()) {
        alert("Please enter a grade name.");
        return;
      }
      
      const newGrade = {
        grade: gradeName,
        data: gradeRows.map((row, index) => ({
          minCarat: row.minCarat,
          maxCarat: row.maxCarat,
          price: prices[index] || 0, 
        })),
      };
      onAdd(newGrade);
    } else {
      if (!minSize || !maxSize) {
        alert("Please enter min and max size.");
        return;
      }

      const newColumn = {
        minSize: parseFloat(minSize),
        maxSize: parseFloat(maxSize),
        prices,
      };

      onAdd(newColumn);
    }
  };

  return (
    <div className={`p-4 bg-white shadow-md rounded-md w-full ${isGradeMode ? 'max-w-lg' : 'max-w-2xl'}`}>
      <h3 className="text-lg font-semibold mb-3 text-gray-700">
        {isGradeMode ? "Add New Grade" : "Add New Sieve Size Column"}
      </h3>

      {/* Grade Name Input (Only in Grade Mode) */}
      {isGradeMode && (
        <div className="mb-3">
          <label className="block text-sm font-medium text-gray-600">Grade Name</label>
          <input
            type="text"
            value={gradeName}
            onChange={(e) => setGradeName(e.target.value)}
            placeholder="Enter grade name"
            className="w-full p-2 border rounded-md text-sm"
          />
        </div>
      )}

      {/* Price Inputs (Dynamic Based on Mode) */}
      {isGradeMode ? (
        gradeRows.length > 0 ? (
          gradeRows.map((row, index) => (
            <div key={index} className="mb-3 flex flex-wrap">
              <label className="block text-sm font-medium text-gray-600">
                {row.minCarat} - {row.maxCarat} Carat Price
              </label>
              <input
                type="number"
                value={prices[index] || ""}
                onChange={(e) => handlePriceChange(index, e.target.value)}
                placeholder="Enter price"
                className="w-full p-2 border rounded-md text-sm"
              />
            </div>
          ))
        ) : (
          <p className="text-red-500">No rows available.</p>
        )
      ) : (
        <div className="border border-gray-300 rounded-md overflow-hidden">
          <div className="bg-gray-100 px-4 py-2 border-b border-gray-300">
            <h4 className="text-sm font-semibold text-gray-700">
              Add New Sieve Size Column
            </h4>
          </div>
          <div className="max-h-64 overflow-y-auto">
            <table className="w-full">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b border-gray-200">
                    Type
                  </th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-600 border-b border-gray-200">
                    Value
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {/* Sieve Size Row */}
                <tr className="hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm text-gray-700 font-medium bg-gray-50">
                    Sieve Size
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={minSize}
                        onChange={(e) => setMinSize(e.target.value)}
                        placeholder="Min"
                        className="w-20 p-2 border rounded-md text-sm text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span className="text-gray-500 font-medium">-</span>
                      <input
                        type="number"
                        value={maxSize}
                        onChange={(e) => setMaxSize(e.target.value)}
                        placeholder="Max"
                        className="w-20 p-2 border rounded-md text-sm text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </td>
                </tr>

                {/* Diamond Type Price Rows */}
                {diamondTypes?.map((diamondType, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm text-gray-700 font-medium bg-gray-50">
                      {diamondType}
                    </td>
                    <td className="px-4 py-3">
                      <input
                        type="number"
                        value={prices[diamondType] || ""}
                        onChange={(e) => handlePriceChange(diamondType, e.target.value)}
                        placeholder="Enter price"
                        className="w-full p-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 mt-4">
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
        >
          {isGradeMode ? "Add Grade" : "Add Column"}
        </button>
        <button
          onClick={onCancel}
          className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}
