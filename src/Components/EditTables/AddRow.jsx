import React, { useState } from "react";

export default function AddRow({ onAdd, onCancel, sieveSizes = [] }) {
  const [diamondType, setDiamondType] = useState("");
  const [prices, setPrices] = useState(
    sieveSizes.reduce((acc, size) => ({ ...acc, [size]: "" }), {})
  );

  const handlePriceChange = (size, value) => {
    setPrices((prev) => ({
      ...prev,
      [size]: value === "" ? "" : value,
    }));
  };

  const handleSubmit = () => {
    if (!diamondType.trim()) return;

    const sizeDetails = sieveSizes.map((size) => {
      const [minSize, maxSize] = size.split('-').map(Number);

      return {
        minSize,
        maxSize,
        manufacturingPrice: prices[size] || "",
      };
    });
    const newRow = {
      diamondType,
      sizes: sizeDetails,
    };
    onAdd(newRow);
  };

  return (
    <div className="p-4 bg-white shadow-md rounded-md">
      <h2 className="text-lg font-semibold mb-2">Add New Row</h2>
      <input
        type="text"
        placeholder="Diamond Type"
        value={diamondType}
        onChange={(e) => setDiamondType(e.target.value)}
        className="border p-2 w-full rounded-md mb-2"
      />
      {/* Use flex to arrange all input fields in a single row */}
      <div className="flex items-center flex-wrap gap-4 mt-2">
        {sieveSizes.map((size) => {
          const [minSize, maxSize] = size.split('-');

          return (
            <div key={size} className="flex items-center">
              <label className="block text-sm font-medium mr-2">{`${minSize} - ${maxSize}`}</label>
              <input
                type="number"
                value={prices[size] === "" ? "" : prices[size]}
                onChange={(e) => handlePriceChange(size, e.target.value)}
                className="border p-2 w-24 rounded-md" 
              />
            </div>
          );
        })}
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <button className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition" onClick={handleSubmit}>
          Add Row
        </button>
        <button className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition" onClick={onCancel}>
          Cancel
        </button>
      </div>
    </div>
  );
}
