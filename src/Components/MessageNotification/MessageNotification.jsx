// components/MessageNotification.js
import React from 'react';

const MessageNotification = ({ message, type }) => {
  let bgColor, textColor;

  switch (type) {
    case 'success':
      bgColor = 'bg-green-100';
      textColor = 'text-green-600';
      break;
    case 'warning':
      bgColor = 'bg-yellow-100';
      textColor = 'text-yellow-600';
      break;
    case 'error':
      bgColor = 'bg-red-100';
      textColor = 'text-red-600';
      break;
    default:
      bgColor = 'bg-gray-100';
      textColor = 'text-gray-600';
      break;
  }

  return (
    message && (
      <div className={`${bgColor} ${textColor} p-4 rounded-md mb-4 text-center shadow-md`}>
        {message}
      </div>
    )
  );
};

export default MessageNotification;
