import React, { useState } from "react";

const DynamicRow = ({ columns, onAddRow, onCancel }) => {
  if (!Array.isArray(columns)) {
    console.error("Expected columns to be an array", columns);
    return null; 
  }

  // Initialize rowData based on columns keys
  const [rowData, setRowData] = useState(
    columns.reduce((acc, col) => {
      acc[col.key] = "";
      return acc;
    }, {})
  );

  // <PERSON><PERSON> changes to input fields
  const handleChange = (key, value) => {
    setRowData((prev) => ({ ...prev, [key]: value }));
  };

  // Handle adding the row data
  const handleAdd = () => {
    const isEmpty = Object.values(rowData).some((value) => value.trim() === "");

    onAddRow(rowData);

    // Reset form after adding
    setRowData((prev) =>
      Object.keys(prev).reduce((acc, key) => {
        acc[key] = "";
        return acc;
      }, {})
    );
  };

  return (
    <div className="flex flex-wrap gap-3 items-center p-4 border rounded-lg shadow-md bg-white">
      {/* Iterate through columns to render each input field */}
      {columns.map(({ key, placeholder, type }) => (
        <input
          key={key}
          type={type || "text"}
          placeholder={placeholder}
          value={rowData[key]}
          onChange={(e) => handleChange(key, e.target.value)}
          className="p-2 border rounded-md w-full sm:w-56 border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      ))}

      {/* Add button for adding the row */}
      <button
        onClick={handleAdd}
        className="px-4 py-2 bg-green-600 cursor-pointer text-white rounded-md hover:bg-green-700 transition"
      >
        Add
      </button>

      {/* Cancel button for canceling the action */}
      <button
        onClick={onCancel}
        className="px-4 py-2 bg-red-600 cursor-pointer text-white rounded-md hover:bg-red-700 transition"
      >
        Cancel
      </button>
    </div>
  );
};

export default DynamicRow;
