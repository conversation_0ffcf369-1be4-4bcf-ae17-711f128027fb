import React from "react";
import { UserCircleIcon, Bars3Icon } from "@heroicons/react/24/outline";
import { useAuth } from "../Context/AuthContext";

const Dashboard = () => {
	const { isLoggedIn, login, logout, isSidebarOpen, toggleSidebar, isDesktop } = useAuth(); 

	return (
		<>
			<main className="flex lg:ml-72 flex-col">
				{/* Header Section */}
				<div className="w-full h-[60px] p-[10px] bg-gray-200 shadow-md flex items-center justify-between">
					
					{/* Sidebar Toggle Button */}
					<div className="flex items-center space-x-3">
						{!isDesktop && (
							<button onClick={toggleSidebar} className="p-2 rounded-md cursor-pointer hover:bg-gray-200 transition">
								<Bars3Icon className="h-6 w-6 text-gray-700" />
							</button>
						)}
					</div>

					{/* Login Button / Profile Icon */}
					<div className="hidden md:flex items-center space-x-3">
						{isLoggedIn ? (
							<div className="flex items-center space-x-2">
								<UserCircleIcon className="h-8 w-8 text-gray-700 cursor-pointer" />
								<button onClick={logout} className="border border-gray-400 cursor-pointer px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition">
									Logout
								</button>
							</div>
						) : (
							<button onClick={login} className="border cursor-pointer border-gray-400 px-4 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition">
								Login
							</button>
						)}
					</div>
				</div>
				
				{/* Dashboard Content */}
				<div className="w-full h-screen bg-white shadow-sm flex flex-col p-6">
					<h1 className="text-4xl font-bold text-gray-800 transition-all duration-300 lg:pl-0">Dashboard</h1>
					
					{/* Centered Content */}
					<div className="flex flex-1 items-center justify-center">
						<p className="text-gray-600 text-lg">No content to show for now</p>
					</div>
				</div>
			</main>
		</>
	);
};

export default Dashboard;
