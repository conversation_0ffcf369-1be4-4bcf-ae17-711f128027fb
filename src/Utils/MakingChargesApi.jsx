import apiClient from "./AxiosConfig";

export const API_BASE_URL = import.meta.env.VITE_BASE_URL;

// fetching data api

export const fetchMakingCharges = async () => {
    try {
      const response = await apiClient.get("/metal-making-charges");
      return response.data.data;
    } catch (error) {
      console.error("Error fetching making charges:", error);
      return [];
    }
  };
  
  //updating making charges api
  export const updateMakingCharges = async (updatedCharges) => {
    try {
      const response = await apiClient.patch("/metal-making-charges",
        updatedCharges,
      );
      return response.data;
    } catch (error) {
      console.error("Error updating making charges:", error);
      throw error;
    }
  };
  
  // adding new row for making chargs api
  export const addMakingCharge = async (newRow) => {
    try {
      const response = await apiClient.post("/metal-making-charges", newRow);
      return response.data;
    } catch (error) {
      console.error("Error adding new making charge:", error.response?.data || error.message);
      throw error;
    }
  };
  
  