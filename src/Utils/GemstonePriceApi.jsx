import apiClient from "./AxiosConfig";

// Fetching Data API
// Small Studded Gemstone (Single Gemstone is less than 0.3 carat)

export const fetchSmallStudded = async () => {
    try {
        const response = await apiClient.get("/gemstone-price/smallStudded");
        return response.data.data;
    } catch (error) {
        console.error("Error fetching metal prices:", error);
        return [];
    }
  };

  // Gemstone, Type Semi-Precious, Single Gemstone Greater Than 0.3 Carat
  export const fetchSemiPreciousStudded = async () => {
    try {
        const response = await apiClient.get("/gemstone-price/semiPrecious");
        return response.data.data;
    } catch (error) {
        console.error("Error fetching metal prices:", error);
        return [];
    }
  };

  // Gemstone, Type Precious, Single Gemstone Greater Than 0.3 Carat
  export const fetchPreciousStudded = async () => {
    try {
        const response = await apiClient.get("/gemstone-price/precious");
        return response.data.data;
    } catch (error) {
        console.error("Error fetching metal prices:", error);
        return [];
    }
  };
  
  // upadate api
  // Small Studded Gemstone (Single Gemstone is less than 0.3 carat)
  export const updateSmallStudded = async (updatedMultiplier) => {
    try {
      const response = await apiClient.patch("/gemstone-price/smallStudded", updatedMultiplier);
      return response;
    } catch (error) {
      console.error("Error updating prices:", error.response || error.message);
      throw error;
    }
  };

  // Gemstone, Type Semi-Precious, Single Gemstone Greater Than 0.3 Carat
  export const updateSemiPreciousStudded = async (updatedMultiplier) => {
    try {
      const response = await apiClient.patch("/gemstone-price/semiPrecious", updatedMultiplier);
      return response;
    } catch (error) {
      console.error("Error updating prices:", error.response || error.message);
      throw error;
    }
  };

  // Gemstone, Type Precious, Single Gemstone Greater Than 0.3 Carat
  export const updatePreciousStudded = async (updatedMultiplier) => {
    try {
      const response = await apiClient.patch("/gemstone-price/precious", updatedMultiplier);
      return response;
    } catch (error) {
      console.error("Error updating prices:", error.response || error.message);
      throw error;
    }
  };
  
  // new row added
  // Small Studded Gemstone (Single Gemstone is less than 0.3 carat)
  export const addRowSmallStudded = async (newRow) => {
    try {
      const response = await apiClient.post("/gemstone-row/smallStudded", newRow);
      return response;
    } catch (error) {
      console.error("Error adding new making charge:", error.response?.data || error.message);
      throw error;
    }
  };

  // Gemstone, Type Semi-Precious, Single Gemstone Greater Than 0.3 Carat
  export const addRowSemiPreciousStudded = async (newRow) => {
    try {
      const response = await apiClient.post("/gemstone-row/semiPrecious", newRow);
      return response;
    } catch (error) {
      console.error("Error adding new making charge:", error.response?.data || error.message);
      throw error;
    }
  };

  // Gemstone, Type Precious, Single Gemstone Greater Than 0.3 Carat
  export const addRowPreciousStudded = async (newRow) => {
    try {
      const response = await apiClient.post("/gemstone-row/precious", newRow);
      return response;
    } catch (error) {
      console.error("Error adding new making charge:", error.response?.data || error.message);
      throw error;
    }
  };