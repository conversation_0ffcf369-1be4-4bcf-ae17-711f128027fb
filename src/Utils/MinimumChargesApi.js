import apiClient from './AxiosConfig';

export const fetchPartialPaymentConfig = async () => {
  try {
    const response = await apiClient.get('/partial-payment-config');
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updatePartialPaymentConfig = async (charges) => {
  try {
    const response = await apiClient.patch('/partial-payment-config', charges);
    return response.data;
  } catch (error) {
    throw error;
  }
};