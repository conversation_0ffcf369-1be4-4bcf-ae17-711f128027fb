import apiClient from "./AxiosConfig";

// Fetching data api
export const getMetalPrices = async () => {
  try {
    const response = await apiClient.get("/metal-price");
    return response.data.data;
  } catch (error) {
    console.error("Error fetching metal prices:", error);
    return [];
  }
};

// Updating handling charges api
export const updateHandlingCharges = async (payload) => {
  try {
    const response = await apiClient.patch(
      "/metal-handling-charges",
      payload,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error updating handling charges:", error);
    throw error;
  }
};

// Adding new row api
export const addMetalPrice = async (payload) => {
  try {
    const response = await apiClient.post("/metal-price", payload, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error) {
    console.error(
      "Error adding metal price:",
      error.response?.data || error.message
    );
    throw error;
  }
};
