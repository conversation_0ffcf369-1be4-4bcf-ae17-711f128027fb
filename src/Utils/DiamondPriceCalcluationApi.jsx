import apiClient from "./AxiosConfig";

// Diamond fetching data api
// Round Shape
export const roundShapeDiamondPrice = async () => {
    try {
        const response = await apiClient.get(`/diamond-price/round`);
        return response.data.data; 
    } catch (error) {
        console.error("Error fetching metal prices:", error);
        return [];
    }
  };
  
  // Other Shape
  export const otherShapeDiamondPrice = async () => {
    try {
        const response = await apiClient.get(`/diamond-price/others`);
        return response.data.data; 
    } catch (error) {
        console.error("Error fetching metal prices:", error);
        return [];
    }
  };
  
  // Updating api of diamond
  // ROund Shape
  export const updateRoundShapeDiamondPrice = async (updatedPrices) => {
    try {
      const response = await apiClient.patch("/diamond-price/round", updatedPrices);
      return response.data;
    } catch (error) {
      console.error("Error updating prices:", error.response?.data || error.message);
      throw error;
    }
  };
  
  // Other shape
  export const updateOtherShapeDiamondPrice = async (updatedPrices) => {
    try {
      const response = await apiClient.patch("/diamond-price/others", updatedPrices);
      return response.data;
    } catch (error) {
      console.error("Error updating prices:", error.response?.data || error.message);
      throw error;
    }
  };
  
  // Adding new row of diamond
  // Round Shape
  export const addRowRoundShapeDiamond = async (payload) => {
    try {
      const response = await apiClient.post(`/diamond-row/round`, Array.isArray(payload) ? payload : [payload], {
        headers: {
          "Content-Type": "application/json",
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error adding round shape diamond row:", error.response?.data || error.message);
      throw error;
    }
  };
  
  // Other Shape
  export const addRowOtherShapeDiamond = async (payload) => {
    try {
      const response = await apiClient.post(`/diamond-row/others`, Array.isArray(payload) ? payload : [payload], {
        headers: {
          "Content-Type": "application/json",
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error adding other shape diamond row:", error.response?.data || error.message);
      throw error;
    }
  };
  
  // Adding New Column of Diamond
  // Round Shape
  export const addRoundshapeColumnDiamond = async (payload) => {
    try {
      const response = await apiClient.post(`/diamond-column/round`, Array.isArray(payload) ? payload : [payload], {
        headers: {
          "Content-Type": "application/json",
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error adding round shape diamond row:", error.response?.data || error.message);
      throw error;
    }
  };
  
  // Other Shape
  export const addOthershapeColumnDiamond = async (payload) => {
    try {
      const response = await apiClient.post(`/diamond-column/others`, Array.isArray(payload) ? payload : [payload], {
        headers: {
          "Content-Type": "application/json",
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error adding other shape diamond row:", error.response?.data || error.message);
      throw error;
    }
  };
  
  
  
  // Multiplier Fetching data
  //Round Shape
  export const roundShapeMultiplier = async () => {
    try {
        const response = await apiClient.get(`/diamond-multiplier/round`);
        return response.data.data; 
    } catch (error) {
        console.error("Error fetching metal prices:", error);
        return [];
    }
  };
  
  // other shape
  export const otherShapeMultiplier = async () => {
    try {
        const response = await apiClient.get(`/diamond-multiplier/others`);
        return response.data.data; 
    } catch (error) {
        console.error("Error fetching metal prices:", error);
        return [];
    }
  };
  
  // Update api of multiplier
  // Round Shape
  export const updateRoundShapeMultiplier = async (updatedMultiplier) => {
    try {
      const response = await apiClient.patch(`/diamond-multiplier/round`, updatedMultiplier);
      return response;
    } catch (error) {
      console.error("Error updating prices:", error.response || error.message);
      throw error;
    }
  };
  
  // Other Shape
  export const updateOthersShapeMultiplier = async (updatedMultiplier) => {
    try {
      const response = await apiClient.patch(`/diamond-multiplier/others`, updatedMultiplier);
      return response;
    } catch (error) {
      console.error("Error updating prices:", error.response || error.message);
      throw error;
    }
  };
  
  // Adding new row in Multiplier API
  
  // Round Shape
  export const addNewRowRoundShapeMultiplier = async (newRow) => {
    try {
      const response = await apiClient.post(`/diamond-multiplier/round`, newRow);
      return response;
    } catch (error) {
      console.error("Error adding new making charge:", error.response?.data || error.message);
      throw error;
    }
  };
  
  // Other shape
  export const addNewRowOthersShapeMultiplier = async (newRow) => {
    try {
      const response = await apiClient.post(`/diamond-multiplier/others`, newRow);
      return response;
    } catch (error) {
      console.error("Error adding new making charge:", error.response?.data || error.message);
      throw error;
    }
  };
  