import apiClient from "./AxiosConfig";

export const API_BASE_URL = import.meta.env.VITE_BASE_URL;

// Login API
export const loginUser = async (credentials) => {
  try {
    const response = await apiClient.post("/login", credentials, {
      headers: {
        "Content-Type": "application/json",
      },

    });

    if (response.data.status === "success") {
      // Extract user data from the nested data object
      const { user } = response.data.data;

      // Store user data in localStorage for persistence across page reloads
      storeUserData(user);

      // Backend automatically sets authentication cookies
      // No need to manually handle tokens on frontend

      return {
        success: true,
        data: {
          user
        },
        message: response.data.message
      };
    } else {
      return {
        success: false,
        message: response.data.message || "Login failed"
      };
    }
  } catch (error) {
    console.error("Login error:", error);
    return {
      success: false,
      message: error.response?.data?.message || "Login failed. Please try again."
    };
  }
};

// Logout API
export const logoutUser = async () => {
  try {
    // Backend handles authentication via cookies automatically
    // No need to manually send Authorization header
    await apiClient.delete("/logout");

    // Clear user data from localStorage
    clearUserData();

    return {
      success: true,
      message: "Logged out successfully"
    };
  } catch (error) {
    console.error("Logout error:", error);
    // Clear user data even if API call fails to ensure user gets logged out
    clearUserData();
    return {
      success: true,
      message: "Logged out successfully"
    };
  }
};

// Refresh Token API
export const refreshAccessToken = async () => {
  try {
    // Backend handles authentication via cookies automatically
    // No need to manually send Authorization header or read cookies
    const response = await apiClient.put("/update-refresh-access", {});

    if (response.data.status === "success") {
      // Backend automatically updates cookies
      // No need to manually handle tokens on frontend
      return {
        success: true,
        message: "Token refreshed successfully"
      };
    } else {
      throw new Error("Token refresh failed");
    }
  } catch (error) {
    console.error("Token refresh error:", error);
    return {
      success: false,
      message: "Session expired. Please login again."
    };
  }
};

// Helper function to check if user is authenticated and get user details
// Since accessToken cookie is HttpOnly, we need to make an API call to verify authentication
export const isAuthenticated = async () => {
  try {
    console.log("Checking authentication..."); // Debug log

    // Check if user is authenticated with a fast endpoint
    await apiClient.get("/metal-price", {
      _isAuthCheck: true
    });

    console.log("User is authenticated, checking for stored user data...");

    // Try to get user data from localStorage first
    try {
      const storedUser = localStorage.getItem('userData');
      if (storedUser) {
        const userData = JSON.parse(storedUser);
        console.log("User details retrieved from localStorage");
        return {
          success: true,
          user: userData
        };
      }
    } catch (storageError) {
      console.log("Could not retrieve user data from localStorage:", storageError);
    }

    // If no stored user data, user is authenticated but we don't have user details
    console.log("User is authenticated but no user details available");
    return {
      success: true,
      user: null
    };
  } catch (error) {
    console.log("Authentication check failed:", error.response?.status || error.message);
    return {
      success: false,
      user: null
    };
  }
};

// Helper function to store user data
export const storeUserData = (userData) => {
  try {
    localStorage.setItem('userData', JSON.stringify(userData));
    console.log("User data stored in localStorage");
  } catch (error) {
    console.log("Error storing user data:", error);
  }
};

// Helper function to clear user data
export const clearUserData = () => {
  try {
    localStorage.removeItem('userData');
    console.log("User data cleared from localStorage");
  } catch (error) {
    console.log("Error clearing user data:", error);
  }
};
