import apiClient from "./AxiosConfig";

// Fetching Price data
// Round Shape
export const roundShapeSolitairePrices = async () => {
  try {
      const response = await apiClient.get("/solitaire-price/round");
      return response.data;
  } catch (error) {
      console.error("Error fetching metal prices:", error);
      return [];
  }
};

// other shape
export const otherShapeSolitairePrices = async () => {
  try {
      const response = await apiClient.get("/solitaire-price/others");
      return response.data;
  } catch (error) {
      console.error("Error fetching metal prices:", error);
      return [];
  }
};

// Fetching Config data
// Round Shape
export const roundShapeSolitaireConfig = async () => {
  try {
      const response = await apiClient.get("/solitaire-config/round");
      return response.data;
  } catch (error) {
      console.error("Error fetching metal prices:", error);
      return [];
  }
};

// other shape
export const otherShapeSolitaireConfig = async () => {
  try {
      const response = await apiClient.get("/solitaire-config/others");
      return response.data;
  } catch (error) {
      console.error("Error fetching metal prices:", error);
      return [];
  }
};

// Updating the data
// Round Shape
export const updateConfigRound = async (updatedMultiplier) => {
  try {
    const response = await apiClient.patch("/solitaire-config/round", updatedMultiplier);
    return response;
  } catch (error) {
    console.error("Error updating prices:", error.response || error.message);
    throw error;
  }
};

// Other Shape
export const updateConfigOther = async (updatedMultiplier) => {
  try {
    const response = await apiClient.patch("/solitaire-config/others", updatedMultiplier);
    return response;
  } catch (error) {
    console.error("Error updating prices:", error.response || error.message);
    throw error;
  }
};

// ROund Shape
export const updatePriceRound = async (updatedMultiplier) => {
  try {
    const response = await apiClient.patch("/solitaire-price/round", updatedMultiplier);
    return response;
  } catch (error) {
    console.error("Error updating prices:", error.response || error.message);
    throw error;
  }
};

// Other shape
export const updatePriceOther = async (updatedMultiplier) => {
  try {
    const response = await apiClient.patch("/solitaire-price/others", updatedMultiplier);
    return response;
  } catch (error) {
    console.error("Error updating prices:", error.response || error.message);
    throw error;
  }
};


// adding the new row
// Round Shape
export const addRoundShapeSolitaireRow = async (newRow) => {
  try {
    const response = await apiClient.post("/solitaire-row/round", newRow);
    return response;
  } catch (error) {
    console.error("Error adding new making charge:", error.response?.data || error.message);
    throw error;
  }
};

// Other shape
export const addOtherShapeSolitaireRow = async (newRow) => {
  try {
    const response = await apiClient.post("/solitaire-row/others", newRow);
    return response;
  } catch (error) {
    console.error("Error adding new making charge:", error.response?.data || error.message);
    throw error;
  }
};

// Adding Column
// Round shape
export const addRoundShapeColumnSolitaire = async (payload) => {
  try {
    const response = await apiClient.post("/solitaire-column/round", Array.isArray(payload) ? payload : [payload], {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error adding round shape diamond row:", error.response?.data || error.message);
    throw error;
  }
};

// Other Shape
export const addOtherShapeColumnSolitaire = async (payload) => {
  try {
    const response = await apiClient.post("/solitaire-column/others", Array.isArray(payload) ? payload : [payload], {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error adding round shape diamond row:", error.response?.data || error.message);
    throw error;
  }
};
