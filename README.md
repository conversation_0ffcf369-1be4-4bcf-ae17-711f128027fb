# React + Vite Project

This project is built using **React** with **Vite**, ensuring fast development with HMR (Hot Module Replacement) and optimized builds. It includes essential libraries such as **Axios** for API handling, **React Context API** for state management, and various utility components.

---

## 🚀 Features
- **Vite + React** for fast and optimized development
- **React Context API** for managing authentication and global state
- **Axios** for API requests and data fetching
- **Tailwind CSS** for efficient styling
- **Dynamic Data Handling** for managing solitaire price calculations
- **Modular Components** such as Action Buttons, Modals, and Notification Messages

---

## 📦 Installation & Setup

1. **Clone the Repository**
   ```sh
   git clone https://github.com/your-repo/your-project.git
   cd your-project
   ```

2. **Install Dependencies**
   ```sh
   npm install
   ```

3. **Start the Development Server**
   ```sh
   npm run dev
   ```
   The project will be available at: `http://localhost:5173`

4. **Build for Production**
   ```sh
   npm run build
   ```

5. **Preview Production Build**
   ```sh
   npm run preview
   ```

---

## 📡 API Integration
This project uses **Axios** for API communication. Ensure the backend API is running and update the `BASE_URL` accordingly.

Example API Call:
```javascript
import axios from 'axios';

const fetchPrices = async () => {
  try {
    const response = await axios.get('/api/solitaire-prices');
    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};
```

---

## 🔧 Project Structure
```
├── src
│   ├── Components
│   │   ├── Button
│   │   │   ├── ActionButton.jsx
│   │   ├── Loader
│   │   │   ├── Loader.jsx
│   │   ├── Modal
│   │   │   ├── ConfirmModal.jsx
│   │   ├── MessageNotification
│   │   │   ├── MessageNotification.jsx
│   │   ├── DynamicRow
│   │   │   ├── DynamicRow.jsx
│   │   ├── Pagination  
│   │   │   ├── Pagination.jsx
│   │   ├── SideBar
│   │   │   ├──Sidebar.jsx
│   │   ├── Tables
│   │   │   ├── DiamondPrice
│   │   │   │   ├── DiamondMultiplierPricesTable.jsx
│   │   │   │   ├── ManufacturingRateTable.jsx
│   │   │   ├── Gemstonetable
│   │   │   │   ├── GemstonePriceTable.jsx   
│   │   │   ├── EditTables
│   │   │   │   ├── AddRow.jsx  
│   ├── Context
│   │   ├── AuthContext.jsx
│   ├── Pages
│   │   ├── MetalPriceCalculation
│   │   │   ├── MetalPriceCalculation.jsx
│   │   ├── MakingCharges
│   │   │   ├── MakingCharges.jsx
│   │   ├── StuddedDiamondPriceCalculation
│   │   │   ├── DiamondPrice.jsx.jsx
│   │   ├── SolitairePriceCalculation
│   │   │   ├── SolitairePrice.jsx
│   │   ├── GemstonePriceCalculation
│   │       ├── GemstonePrice.jsx
│   ├── App.jsx
│   ├── main.jsx
├── public
│   ├── index.html
├── package.json
├── vite.config.js
```
---

## 🧪 Running Tests
If want to run tests configured (e.g., Jest, React Testing Library):
```sh
npm run test
```

---

## 🔥 Hot Reloading
Vite provides HMR (Hot Module Replacement), allowing instant updates without a full reload. This significantly speeds up development.

---

